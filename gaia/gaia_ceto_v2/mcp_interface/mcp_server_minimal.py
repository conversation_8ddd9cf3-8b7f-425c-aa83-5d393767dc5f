"""
Minimal MCP Server - A simplified version to test basic functionality.
"""

import asyncio
import logging
from fastmcp import FastMCP

logger = logging.getLogger(__name__)

# Create a minimal MCP server
mcp = FastMCP("gaia_ceto_v2_minimal")

@mcp.tool()
def echostring(text: str) -> str:
    """
    Echoes the given text.
    """
    logger.info(f"Echo called with: {text}")
    return f"Echo: {text}"

@mcp.tool()
def add_numbers(a: int, b: int) -> int:
    """
    Adds two numbers together.
    """
    result = a + b
    logger.info(f"Add called: {a} + {b} = {result}")
    return result

async def run_server(host: str = "127.0.0.1", port: int = 9001):
    """
    Runs the minimal MCP server.
    """
    logger.info(f"Starting minimal MCP server on {host}:{port}")
    await mcp.run(host=host, port=port)

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    try:
        asyncio.run(run_server())
    except KeyboardInterrupt:
        logger.info("Shutting down minimal server.")
